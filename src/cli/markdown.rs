use crate::api::models::ResponseApiResponse;
use crate::utils::content::extract_response_content;
use anyhow::Result;

use regex::Regex;
use std::sync::OnceLock;
use syntect::easy::HighlightLines;
use syntect::highlighting::{Style, ThemeSet, Color as SyntectColor};
use syntect::parsing::SyntaxSet;
use syntect::parsing::SyntaxReference;
use termimad::MadSkin;

#[derive(Debug, Clone)]
pub enum ContentBlock {
    Markdown(String),
    CodeBlock {
        language: String,
        content: String,
    },
}

pub struct HybridRenderer {
    skin: MadSkin,
    enable_syntax_highlighting: bool,
}

static SYNTAX_HIGHLIGHTER: OnceLock<(SyntaxSet, ThemeSet)> = OnceLock::new();

impl HybridRenderer {
    pub fn new() -> Self {
        Self::with_syntax_highlighting(true)
    }

    pub fn with_syntax_highlighting(enable_syntax_highlighting: bool) -> Self {
        let mut skin = MadSkin::default();
        
        // Customize the skin for better terminal rendering
        skin.set_headers_fg(crossterm::style::Color::Cyan);
        skin.bold.set_fg(crossterm::style::Color::Yellow);
        skin.italic.set_fg(crossterm::style::Color::Magenta);
        skin.inline_code.set_fg(crossterm::style::Color::Green);
        skin.code_block.set_fg(crossterm::style::Color::Green);
        
        Self { 
            skin,
            enable_syntax_highlighting,
        }
    }

    fn get_syntax_set(&self) -> &SyntaxSet {
        &SYNTAX_HIGHLIGHTER.get_or_init(|| {
            (SyntaxSet::load_defaults_newlines(), ThemeSet::load_defaults())
        }).0
    }

    fn get_theme_set(&self) -> &ThemeSet {
        &SYNTAX_HIGHLIGHTER.get_or_init(|| {
            (SyntaxSet::load_defaults_newlines(), ThemeSet::load_defaults())
        }).1
    }

    pub fn render_response(&self, response: &ResponseApiResponse, verbose: bool) -> Result<()> {
        if response.output.is_empty() {
            return Err(anyhow::anyhow!("No output messages returned from OpenAI"));
        }

        println!("\n📝 Response:");
        let content = extract_response_content(response);
        
        // Check if content contains markdown-like patterns
        if self.looks_like_markdown(&content) {
            // Use hybrid rendering for markdown content
            self.render_hybrid_markdown(&content)?;
        } else {
            // Fallback to plain text
            println!("{}", content);
        }

        if verbose {
            self.render_verbose_info(response)?;
        }

        Ok(())
    }

    fn render_hybrid_markdown(&self, content: &str) -> Result<()> {
        let blocks = self.parse_content_blocks(content)?;
        
        for block in blocks {
            match block {
                ContentBlock::Markdown(markdown) => {
                    // Render regular markdown through termimad
                    let rendered = self.skin.term_text(&markdown);
                    print!("{}", rendered);
                },
                ContentBlock::CodeBlock { language, content } => {
                    // Render code blocks directly to terminal
                    self.render_code_block_direct(&content, &language)?;
                }
            }
        }
        
        Ok(())
    }

    fn parse_content_blocks(&self, content: &str) -> Result<Vec<ContentBlock>> {
        let mut blocks = Vec::new();
        let code_block_regex = Regex::new(r"```(\w+)?\n([\s\S]*?)```")?;

        let mut last_end = 0;

        for captures in code_block_regex.captures_iter(content) {
            let full_match = captures.get(0).unwrap();
            let match_start = full_match.start();
            let match_end = full_match.end();
            
            // Add any markdown content before this code block
            if match_start > last_end {
                let markdown_content = &content[last_end..match_start];
                if !markdown_content.trim().is_empty() {
                    blocks.push(ContentBlock::Markdown(markdown_content.to_string()));
                }
            }
            
            // Add the code block
            let language = captures.get(1).map(|m| m.as_str()).unwrap_or("").to_string();
            let code_content = captures.get(2).unwrap().as_str().to_string();

            blocks.push(ContentBlock::CodeBlock {
                language,
                content: code_content,
            });
            
            last_end = match_end;
        }
        
        // Add any remaining markdown content
        if last_end < content.len() {
            let remaining = &content[last_end..];
            if !remaining.trim().is_empty() {
                blocks.push(ContentBlock::Markdown(remaining.to_string()));
            }
        }
        
        // If no code blocks found, treat entire content as markdown
        if blocks.is_empty() {
            blocks.push(ContentBlock::Markdown(content.to_string()));
        }
        
        Ok(blocks)
    }

    fn render_code_block_direct(&self, code: &str, language: &str) -> Result<()> {
        if !self.enable_syntax_highlighting {
            // Just print the code block without highlighting
            println!("{}", code);
            return Ok(());
        }

        // Find appropriate syntax definition
        let syntax = self.find_syntax_definition(language);
        let theme = &self.get_theme_set().themes["base16-ocean.dark"];

        let mut highlighter = HighlightLines::new(syntax, theme);

        // Process each line using a more efficient approach
        for line in code.lines() {
            let ranges: Vec<(Style, &str)> = highlighter.highlight_line(line, self.get_syntax_set())?;

            // Build the line using simpler ANSI codes to avoid excessive length
            let mut line_parts = Vec::new();

            for (style, text) in ranges {
                if text.is_empty() {
                    continue;
                }

                // Use simpler ANSI color codes instead of crossterm's verbose RGB codes
                let color_code = self.syntect_color_to_simple_ansi(style.foreground);
                let bold_code = if style.font_style.contains(syntect::highlighting::FontStyle::BOLD) { "\x1b[1m" } else { "" };
                let italic_code = if style.font_style.contains(syntect::highlighting::FontStyle::ITALIC) { "\x1b[3m" } else { "" };
                let underline_code = if style.font_style.contains(syntect::highlighting::FontStyle::UNDERLINE) { "\x1b[4m" } else { "" };

                // Combine styling codes with text and reset
                let styled_text = format!("{}{}{}{}{}\x1b[0m", color_code, bold_code, italic_code, underline_code, text);
                line_parts.push(styled_text);
            }

            // Join all parts and print the complete line
            let complete_line = line_parts.join("");
            println!("{}", complete_line);
        }

        Ok(())
    }

    fn find_syntax_definition(&self, language: &str) -> &SyntaxReference {
        if language.is_empty() {
            return self.get_syntax_set().find_syntax_plain_text();
        }

        let normalized_lang = language.to_lowercase();
        
        self.get_syntax_set()
            .find_syntax_by_extension(&normalized_lang)
            .or_else(|| {
                // Try common language name mappings
                let lang_name = match normalized_lang.as_str() {
                    "rust" => "rs",
                    "python" => "py",
                    "javascript" | "js" => "js",
                    "typescript" | "ts" => "ts",
                    "c++" | "cpp" => "cpp",
                    "c#" | "csharp" => "cs",
                    "golang" | "go" => "go",
                    "shell" | "bash" => "sh",
                    _ => &normalized_lang,
                };
                self.get_syntax_set().find_syntax_by_extension(lang_name)
            })
            .or_else(|| self.get_syntax_set().find_syntax_by_name(language))
            .unwrap_or_else(|| self.get_syntax_set().find_syntax_plain_text())
    }

    fn syntect_color_to_simple_ansi(&self, syntect_color: SyntectColor) -> &'static str {
        // Map syntect RGB color to simple ANSI color codes (much shorter than crossterm's RGB codes)
        match (syntect_color.r, syntect_color.g, syntect_color.b) {
            // Basic colors using simple ANSI codes
            (255, 255, 255) => "\x1b[97m", // Bright White
            (0, 0, 0) => "\x1b[30m",       // Black
            (255, 0, 0) => "\x1b[91m",     // Bright Red
            (0, 255, 0) => "\x1b[92m",     // Bright Green
            (0, 0, 255) => "\x1b[94m",     // Bright Blue
            (255, 255, 0) => "\x1b[93m",   // Bright Yellow
            (255, 0, 255) => "\x1b[95m",   // Bright Magenta
            (0, 255, 255) => "\x1b[96m",   // Bright Cyan

            // For other colors, map to nearest basic ANSI color with bright variants for better visibility
            (r, g, b) => {
                let brightness = (r as f32 * 0.299 + g as f32 * 0.587 + b as f32 * 0.114) / 255.0;

                if brightness > 0.8 {
                    "\x1b[97m" // Bright White
                } else if brightness < 0.2 {
                    "\x1b[30m" // Black
                } else if r > g && r > b {
                    if r > 128 { "\x1b[91m" } else { "\x1b[31m" } // Bright Red or Red
                } else if g > r && g > b {
                    if g > 128 { "\x1b[92m" } else { "\x1b[32m" } // Bright Green or Green
                } else if b > r && b > g {
                    if b > 128 { "\x1b[94m" } else { "\x1b[34m" } // Bright Blue or Blue
                } else if r > 128 && g > 128 {
                    "\x1b[93m" // Bright Yellow
                } else if r > 128 && b > 128 {
                    "\x1b[95m" // Bright Magenta
                } else if g > 128 && b > 128 {
                    "\x1b[96m" // Bright Cyan
                } else {
                    "\x1b[97m" // Default to bright white for better visibility
                }
            }
        }
    }



    pub fn looks_like_markdown(&self, text: &str) -> bool {
        // Simple heuristics to detect markdown content
        text.contains("**") || 
        text.contains("*") || 
        text.contains("`") || 
        text.contains("# ") || 
        text.contains("## ") || 
        text.contains("- ") || 
        text.contains("1. ") ||
        text.contains("```") ||
        text.contains("> ")
    }

    pub fn set_syntax_highlighting(&mut self, enable: bool) {
        self.enable_syntax_highlighting = enable;
    }

    pub fn is_syntax_highlighting_enabled(&self) -> bool {
        self.enable_syntax_highlighting
    }

    fn render_verbose_info(&self, response: &ResponseApiResponse) -> Result<()> {
        let verbose_content = self.format_verbose_info(response);
        // Use hybrid rendering for verbose info as well
        self.render_hybrid_markdown(&verbose_content)?;
        Ok(())
    }

    fn format_verbose_info(&self, response: &ResponseApiResponse) -> String {
        let mut info = String::new();
        
        info.push_str("\n## 📊 Response Metadata\n\n");
        info.push_str(&format!("- **Response ID**: `{}`\n", response.id));
        info.push_str(&format!("- **Model**: `{}`\n", response.model));
        info.push_str(&format!("- **Created At**: `{}`\n", response.created_at));
        info.push_str(&format!("- **Status**: `{}`\n", response.status));
        info.push_str(&format!("- **Object**: `{}`\n", response.object));

        if !response.output.is_empty() {
            let first_output = &response.output[0];
            info.push_str(&format!("- **Message ID**: `{}`\n", first_output.id));
            info.push_str(&format!("- **Message Status**: `{}`\n", first_output.status));
            info.push_str(&format!("- **Role**: `{}`\n", first_output.role));
        }

        info.push_str("\n## 📊 Configuration\n\n");
        info.push_str(&format!("- **Temperature**: `{}`\n", response.temperature));
        info.push_str(&format!("- **Top P**: `{}`\n", response.top_p));
        info.push_str(&format!("- **Tool Choice**: `{}`\n", response.tool_choice));
        info.push_str(&format!("- **Truncation**: `{}`\n", response.truncation));
        info.push_str(&format!("- **Store**: `{}`\n", response.store));
        info.push_str(&format!("- **Parallel Tool Calls**: `{}`\n", response.parallel_tool_calls));

        info.push_str("\n## 📊 Token Usage\n\n");
        info.push_str(&format!("- **Input tokens**: `{}`\n", response.usage.input_tokens));
        info.push_str(&format!("- **Output tokens**: `{}`\n", response.usage.output_tokens));
        info.push_str(&format!("- **Total tokens**: `{}`\n", response.usage.total_tokens));

        if let Some(input_details) = &response.usage.input_tokens_details {
            if let Some(cached) = input_details.cached_tokens {
                info.push_str(&format!("- **Cached tokens**: `{}`\n", cached));
            }
        }

        if let Some(output_details) = &response.usage.output_tokens_details {
            if let Some(reasoning) = output_details.reasoning_tokens {
                info.push_str(&format!("- **Reasoning tokens**: `{}`\n", reasoning));
            }
        }

        if let Some(reasoning) = &response.reasoning {
            info.push_str("\n## 📊 Reasoning\n\n");
            if let Some(effort) = &reasoning.effort {
                info.push_str(&format!("- **Effort**: `{}`\n", effort));
            }
            if let Some(summary) = &reasoning.summary {
                info.push_str(&format!("- **Summary**: {}\n", summary));
            }
        }

        if let Some(previous_id) = &response.previous_response_id {
            info.push_str(&format!("- **Previous Response ID**: `{}`\n", previous_id));
        }

        info
    }
}

impl Default for HybridRenderer {
    fn default() -> Self {
        Self::new()
    }
}

// Legacy compatibility - this is the old MarkdownRenderer
pub struct MarkdownRenderer {
    hybrid_renderer: HybridRenderer,
}

impl MarkdownRenderer {
    pub fn new() -> Self {
        Self {
            hybrid_renderer: HybridRenderer::new(),
        }
    }

    pub fn with_syntax_highlighting(enable_syntax_highlighting: bool) -> Self {
        Self {
            hybrid_renderer: HybridRenderer::with_syntax_highlighting(enable_syntax_highlighting),
        }
    }

    pub fn render_response(&self, response: &ResponseApiResponse, verbose: bool) -> Result<()> {
        self.hybrid_renderer.render_response(response, verbose)
    }

    pub fn looks_like_markdown(&self, text: &str) -> bool {
        self.hybrid_renderer.looks_like_markdown(text)
    }

    pub fn set_syntax_highlighting(&mut self, enable: bool) {
        self.hybrid_renderer.set_syntax_highlighting(enable);
    }

    pub fn is_syntax_highlighting_enabled(&self) -> bool {
        self.hybrid_renderer.is_syntax_highlighting_enabled()
    }
}

impl Default for MarkdownRenderer {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hybrid_renderer_line_breaking() {
        let renderer = HybridRenderer::new();
        let test_content = r#"Here is a simple Rust function:

```rust
fn main() {
    println!("Hello, world!");
    let mut counter = 0;
    for i in 1..=10 {
        counter += i;
        println!("Counter is now: {}", counter);
    }
}
```

And here is some Python code:

```python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    else:
        return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# Test the function
for i in range(10):
    print(f"Fibonacci({i}) = {calculate_fibonacci(i)}")
```

This demonstrates both **bold text** and *italic text* alongside code blocks."#;

        // Test that rendering doesn't panic
        let result = renderer.render_hybrid_markdown(test_content);
        assert!(result.is_ok());
    }

    #[test]
    fn test_content_block_parsing() {
        let renderer = HybridRenderer::new();
        let test_content = r#"Regular text

```rust
fn main() {
    println!("Hello, world!");
}
```

More regular text

```python
print("Hello, Python!")
```

Final text"#;

        let blocks = renderer.parse_content_blocks(test_content).unwrap();
        assert_eq!(blocks.len(), 5); // 3 markdown blocks + 2 code blocks
        
        // Check that code blocks are properly separated
        if let ContentBlock::CodeBlock { language, content } = &blocks[1] {
            assert_eq!(language, "rust");
            assert!(content.contains("fn main()"));
            assert!(content.contains("println!(\"Hello, world!\");"));
        } else {
            panic!("Expected code block");
        }
    }
}