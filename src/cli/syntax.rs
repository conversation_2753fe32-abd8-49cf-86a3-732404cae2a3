use anyhow::Result;
use crossterm::style::{Color, Stylize};
use regex::Regex;
use std::sync::OnceLock;
use syntect::easy::HighlightLines;
use syntect::highlighting::{Style, ThemeSet, Color as SyntectColor};
use syntect::parsing::SyntaxSet;

pub struct SyntaxHighlighter {
    syntax_set: SyntaxSet,
    theme_set: ThemeSet,
}

static SYNTAX_HIGHLIGHTER: OnceLock<SyntaxHighlighter> = OnceLock::new();

impl SyntaxHighlighter {
    pub fn new() -> Self {
        Self {
            syntax_set: SyntaxSet::load_defaults_newlines(),
            theme_set: ThemeSet::load_defaults(),
        }
    }

    pub fn global() -> &'static SyntaxHighlighter {
        SYNTAX_HIGHLIGHTER.get_or_init(|| SyntaxHighlighter::new())
    }

    pub fn highlight_code_blocks(&self, content: &str, enable_highlighting: bool) -> Result<String> {
        if !enable_highlighting {
            return Ok(content.to_string());
        }

        // Regex to match code blocks with language specifiers
        let code_block_regex = Regex::new(r"```(\w+)?\n([\s\S]*?)```")?;
        
        let mut result = content.to_string();
        
        // Process each code block
        for captures in code_block_regex.captures_iter(content) {
            let full_match = captures.get(0).unwrap().as_str();
            let language = captures.get(1).map(|m| m.as_str()).unwrap_or("");
            let code = captures.get(2).unwrap().as_str();
            
            let highlighted = self.highlight_code(code, language)?;
            // Keep the code block markers but with enhanced content
            let replacement = format!("```{}\n{}\n```", language, highlighted);
            
            result = result.replace(full_match, &replacement);
        }
        
        Ok(result)
    }

    fn highlight_code(&self, code: &str, language: &str) -> Result<String> {
        // Choose theme based on terminal background (defaulting to dark theme)
        let theme = &self.theme_set.themes["base16-ocean.dark"];
        
        // Find syntax definition for the language
        let syntax = if language.is_empty() {
            // If no language specified, try to guess or default to plain text
            self.syntax_set.find_syntax_plain_text()
        } else {
            // Try multiple ways to find the syntax:
            // 1. By extension (e.g., "rs", "py", "js")
            // 2. By name (e.g., "Rust", "Python", "JavaScript")
            // 3. By common aliases
            let normalized_lang = language.to_lowercase();
            
            self.syntax_set
                .find_syntax_by_extension(&normalized_lang)
                .or_else(|| {
                    // Try common language name mappings
                    let lang_name = match normalized_lang.as_str() {
                        "rust" => "rs",
                        "python" => "py",
                        "javascript" | "js" => "js",
                        "typescript" | "ts" => "ts",
                        "c++" | "cpp" => "cpp",
                        "c#" | "csharp" => "cs",
                        "golang" | "go" => "go",
                        "shell" | "bash" => "sh",
                        _ => &normalized_lang,
                    };
                    self.syntax_set.find_syntax_by_extension(lang_name)
                })
                .or_else(|| self.syntax_set.find_syntax_by_name(language))
                .or_else(|| self.syntax_set.find_syntax_by_first_line(code))
                .unwrap_or_else(|| self.syntax_set.find_syntax_plain_text())
        };

        let mut highlighter = HighlightLines::new(syntax, theme);
        let mut highlighted_lines = Vec::new();

        for line in code.lines() {
            let ranges: Vec<(Style, &str)> = highlighter.highlight_line(line, &self.syntax_set)?;
            let styled_line = self.apply_styles_to_line(&ranges);
            highlighted_lines.push(styled_line);
        }

        Ok(highlighted_lines.join("\n"))
    }

    fn apply_styles_to_line(&self, ranges: &[(Style, &str)]) -> String {
        let mut result = String::new();
        
        for (style, text) in ranges {
            if text.is_empty() {
                continue;
            }
            
            // Convert syntect color to crossterm color
            let color = self.syntect_color_to_crossterm(style.foreground);
            
            // Apply styling using crossterm's styling
            let styled_text = if style.font_style.contains(syntect::highlighting::FontStyle::BOLD) {
                text.with(color).bold().to_string()
            } else if style.font_style.contains(syntect::highlighting::FontStyle::ITALIC) {
                text.with(color).italic().to_string()
            } else if style.font_style.contains(syntect::highlighting::FontStyle::UNDERLINE) {
                text.with(color).underlined().to_string()
            } else {
                text.with(color).to_string()
            };
            
            result.push_str(&styled_text);
        }
        
        result
    }

    fn syntect_color_to_crossterm(&self, syntect_color: SyntectColor) -> Color {
        // Map syntect RGB color to crossterm color
        match (syntect_color.r, syntect_color.g, syntect_color.b) {
            // Check for common colors first for better compatibility
            (255, 255, 255) => Color::White,
            (0, 0, 0) => Color::Black,
            (255, 0, 0) => Color::Red,
            (0, 255, 0) => Color::Green,
            (0, 0, 255) => Color::Blue,
            (255, 255, 0) => Color::Yellow,
            (255, 0, 255) => Color::Magenta,
            (0, 255, 255) => Color::Cyan,
            
            // For other colors, use RGB if supported, otherwise map to nearest basic color
            (r, g, b) => {
                // Try RGB first for terminals that support it
                if self.supports_rgb_colors() {
                    Color::Rgb { r, g, b }
                } else {
                    // Map to nearest basic ANSI color for better compatibility
                    self.map_to_ansi_color(r, g, b)
                }
            }
        }
    }

    fn supports_rgb_colors(&self) -> bool {
        // Check if terminal supports RGB colors
        // This is a simple heuristic - in a real implementation you might check
        // environment variables like COLORTERM or terminal capabilities
        std::env::var("COLORTERM").map_or(false, |v| v.contains("truecolor") || v.contains("24bit"))
            || std::env::var("TERM").map_or(false, |v| v.contains("256") || v.contains("color"))
    }

    fn map_to_ansi_color(&self, r: u8, g: u8, b: u8) -> Color {
        // Simple mapping to nearest ANSI color based on RGB values
        let brightness = (r as f32 * 0.299 + g as f32 * 0.587 + b as f32 * 0.114) / 255.0;
        
        if brightness > 0.8 {
            Color::White
        } else if brightness < 0.2 {
            Color::Black
        } else if r > g && r > b {
            if r > 128 { Color::Red } else { Color::DarkRed }
        } else if g > r && g > b {
            if g > 128 { Color::Green } else { Color::DarkGreen }
        } else if b > r && b > g {
            if b > 128 { Color::Blue } else { Color::DarkBlue }
        } else if r > 128 && g > 128 {
            Color::Yellow
        } else if r > 128 && b > 128 {
            Color::Magenta
        } else if g > 128 && b > 128 {
            Color::Cyan
        } else {
            Color::Grey
        }
    }

    pub fn get_supported_languages(&self) -> Vec<String> {
        self.syntax_set
            .syntaxes()
            .iter()
            .flat_map(|syntax| syntax.file_extensions.iter())
            .cloned()
            .collect()
    }

    pub fn is_language_supported(&self, language: &str) -> bool {
        self.syntax_set.find_syntax_by_extension(language).is_some()
            || self.syntax_set.find_syntax_by_name(language).is_some()
    }
}

impl Default for SyntaxHighlighter {
    fn default() -> Self {
        Self::new()
    }
}

pub fn highlight_code_blocks(content: &str, enable_highlighting: bool) -> Result<String> {
    SyntaxHighlighter::global().highlight_code_blocks(content, enable_highlighting)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_syntax_highlighter_creation() {
        let highlighter = SyntaxHighlighter::new();
        assert!(!highlighter.get_supported_languages().is_empty());
    }

    #[test]
    fn test_language_support() {
        let highlighter = SyntaxHighlighter::new();
        
        // Test with known file extensions and language names
        assert!(highlighter.is_language_supported("rs")); // Rust extension
        assert!(highlighter.is_language_supported("py")); // Python extension
        assert!(highlighter.is_language_supported("js")); // JavaScript extension
        
        // Test that non-existent language is not supported
        assert!(!highlighter.is_language_supported("nonexistent"));
    }

    #[test]
    fn test_code_block_detection() {
        let content = r#"
Here's some text.

```rust
fn main() {
    println!("Hello, world!");
}
```

More text here.

```python
print("Hello, Python!")
```
"#;

        let highlighter = SyntaxHighlighter::new();
        let result = highlighter.highlight_code_blocks(content, true);
        assert!(result.is_ok());
        
        let highlighted = result.unwrap();
        assert!(highlighted.contains("```rust"));
        assert!(highlighted.contains("```python"));
    }

    #[test]
    fn test_disabled_highlighting() {
        let content = r#"
```rust
fn main() {
    println!("Hello, world!");
}
```
"#;

        let result = highlight_code_blocks(content, false);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), content);
    }

    #[test]
    fn test_code_block_without_language() {
        let content = r#"
```
some generic code
```
"#;

        let result = highlight_code_blocks(content, true);
        assert!(result.is_ok());
        assert!(result.unwrap().contains("```"));
    }

    #[test]
    fn test_color_mapping() {
        let highlighter = SyntaxHighlighter::new();
        
        // Test basic color mapping
        let white = SyntectColor { r: 255, g: 255, b: 255, a: 255 };
        let mapped = highlighter.syntect_color_to_crossterm(white);
        assert!(matches!(mapped, Color::White));
        
        let black = SyntectColor { r: 0, g: 0, b: 0, a: 255 };
        let mapped = highlighter.syntect_color_to_crossterm(black);
        assert!(matches!(mapped, Color::Black));
    }

    #[test]
    fn test_apply_styles_to_line() {
        let highlighter = SyntaxHighlighter::new();
        
        // Create test style and text
        let style = Style {
            foreground: SyntectColor { r: 255, g: 0, b: 0, a: 255 }, // Red
            background: SyntectColor { r: 0, g: 0, b: 0, a: 255 },   // Black
            font_style: syntect::highlighting::FontStyle::BOLD,
        };
        
        let ranges = vec![(style, "test")];
        let result = highlighter.apply_styles_to_line(&ranges);
        
        // The result should contain styled text (exact format depends on crossterm)
        assert!(!result.is_empty());
        assert!(result.contains("test"));
    }
}