# Comprehensive Code Rendering Test

## Rust Code Test - Complex Example

```rust
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

// Test complex Rust syntax with long lines
pub struct DataProcessor<T> where T: Clone + Send + Sync + 'static {
    data: Arc<Mutex<HashMap<String, T>>>,
    processors: Vec<Box<dyn Fn(&T) -> Result<String, Box<dyn std::error::Error>> + Send + Sync>>,
}

impl<T> DataProcessor<T> where T: Clone + Send + Sync + 'static {
    pub fn new() -> Self {
        Self {
            data: Arc::new(Mutex::new(HashMap::new())),
            processors: Vec::new(),
        }
    }

    // Very long function signature to test line breaking
    pub fn process_with_complex_parameters(&self, key: &str, value: T, transformation_function: impl Fn(&T) -> Result<String, Box<dyn std::error::Error>>, validation_function: impl Fn(&T) -> bool, error_handler: impl Fn(Box<dyn std::error::Error>) -> String) -> Result<String, String> {
        if !validation_function(&value) {
            return Err("Validation failed".to_string());
        }

        match transformation_function(&value) {
            Ok(result) => Ok(result),
            Err(e) => Err(error_handler(e)),
        }
    }
}
```

## Python Code Test - Complex Example

```python
import asyncio
import typing
from dataclasses import dataclass
from typing import Dict, List, Optional, Union, Callable, Any

@dataclass
class DataProcessor:
    data: Dict[str, Any]
    processors: List[Callable[[Any], Union[str, Exception]]]

    def __init__(self):
        self.data = {}
        self.processors = []

    # Very long function signature to test line breaking
    async def process_with_complex_parameters(self, key: str, value: Any, transformation_function: Callable[[Any], Union[str, Exception]], validation_function: Callable[[Any], bool], error_handler: Callable[[Exception], str]) -> Union[str, Exception]:
        """Process data with complex parameter validation and transformation."""
        if not validation_function(value):
            return Exception("Validation failed")

        try:
            result = transformation_function(value)
            if isinstance(result, Exception):
                return error_handler(result)
            return result
        except Exception as e:
            return error_handler(e)

    def batch_process(self, items: List[Dict[str, Any]]) -> List[Union[str, Exception]]:
        return [self.process_item(item) for item in items if self.validate_item(item)]

if __name__ == "__main__":
    processor = DataProcessor()
    asyncio.run(processor.process_with_complex_parameters("test", {"data": "value"}, lambda x: str(x), lambda x: isinstance(x, dict), lambda e: f"Error: {e}"))
```

## JavaScript Code Test

```javascript
class DataProcessor {
    constructor() {
        this.data = new Map();
        this.processors = [];
    }

    // Very long method with complex parameters
    async processWithComplexParameters(key, value, transformationFunction, validationFunction, errorHandler) {
        if (!validationFunction(value)) {
            throw new Error("Validation failed");
        }

        try {
            const result = await transformationFunction(value);
            return result;
        } catch (error) {
            return errorHandler(error);
        }
    }
}

const processor = new DataProcessor();
processor.processWithComplexParameters("test", {data: "value"}, async (x) => JSON.stringify(x), (x) => typeof x === "object", (e) => `Error: ${e.message}`);
```

This comprehensive test should demonstrate that both Rust and Python syntax highlighting work correctly with proper line formatting.
