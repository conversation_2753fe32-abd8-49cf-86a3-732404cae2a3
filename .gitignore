# This file should only ignore things that are generated during a `x.py` build,
# generated by common IDEs, and optional files controlled by the user that
# affect the build (such as bootstrap.toml).
# In particular, things like `mir_dump` should not be listed here; they are only
# created during manual debugging and many people like to clean up instead of
# having git ignore such leftovers. You can use `.git/info/exclude` to
# configure your local ignore list.

## File system
.DS_Store
desktop.ini

## Editor
*.swp
*.swo
Session.vim
.cproject
.idea
*.iml
.vscode
.project
.vim/
.helix/
.zed/
.favorites.json
.settings/
.vs/
.dir-locals.el

## Tool
.valgrindrc
.cargo
# Included because it is part of the test case
!/tests/run-make/thumb-none-qemu/example/.cargo

## Configuration
/bootstrap.toml
/config.toml
/Makefile
config.mk
config.stamp
no_llvm_build

## Build
/dl/
/doc/
/inst/
/llvm/
/mingw-build/
/build
/build-rust-analyzer/
/dist/
/unicode-downloads
/target
/library/target
/src/bootstrap/target
/src/ci/citool/target
/src/tools/x/target
# Created by `x vendor`
/vendor
# Created by default with `src/ci/docker/run.sh`
/obj/
# Created by nix dev shell / .envrc
src/tools/nix-dev-shell/flake.lock

## ICE reports
rustc-ice-*.txt

## Temporary files
*~
\#*
\#*\#
.#*

## Tags
tags
tags.*
TAGS
TAGS.*

## Python
__pycache__/
*.py[cod]
*$py.class

## Node
node_modules
package-lock.json
package.json
/src/doc/rustc-dev-guide/mermaid.min.js

## Rustdoc GUI tests
tests/rustdoc-gui/src/**.lock

## direnv
/.envrc
/.direnv/

## nix
/flake.nix
flake.lock
/default.nix

.env
