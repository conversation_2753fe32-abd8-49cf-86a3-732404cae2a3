[package]
name = "openai-api"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.98"
clap = { version = "4.5.40", features = ["derive"] }
crossterm = "0.27"
dotenvy = "0.15"
regex = "1.0"
reqwest = { version = "0.12.22", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
syntect = "5.0"
termimad = "0.28"
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full"] }
