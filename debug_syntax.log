DEBUG: Parsing content, looking for code blocks...
DEBUG: Found code block with language: 'rust', content length: 699
DEBUG: Found code block with language: 'python', content length: 680
DEBUG: Language: 'rust', Syntax name: 'Rust'
DEBUG: Processing 17 lines for language 'rust'
DEBUG: Line 0: 'fn calculate_fibonacci_sequence(n: usize) -> Vec<u64> {' has 18 fragments
DEBUG:   Fragment 0: 'fn' color=(180,142,173)
DEBUG:   Fragment 1: ' ' color=(192,197,206)
DEBUG:   Fragment 2: 'calculate_fibonacci_sequence' color=(143,161,179)
DEBUG:   Fragment 3: '(' color=(192,197,206)
DEBUG:   Fragment 4: 'n' color=(191,97,106)
DEBUG:   Fragment 5: ':' color=(192,197,206)
DEBUG:   Fragment 6: ' ' color=(192,197,206)
DEBUG:   Fragment 7: 'usize' color=(180,142,173)
DEBUG:   Fragment 8: ')' color=(192,197,206)
DEBUG:   Fragment 9: ' ' color=(192,197,206)
DEBUG:   Fragment 10: '->' color=(192,197,206)
DEBUG:   Fragment 11: ' ' color=(192,197,206)
DEBUG:   Fragment 12: 'Vec' color=(192,197,206)
DEBUG:   Fragment 13: '<' color=(192,197,206)
DEBUG:   Fragment 14: 'u64' color=(180,142,173)
DEBUG:   Fragment 15: '>' color=(192,197,206)
DEBUG:   Fragment 16: ' ' color=(192,197,206)
DEBUG:   Fragment 17: '{' color=(192,197,206)
DEBUG: Line 1: '    let mut sequence = vec![0, 1];' has 15 fragments
DEBUG:   Fragment 0: '    ' color=(192,197,206)
DEBUG:   Fragment 1: 'let' color=(180,142,173)
DEBUG:   Fragment 2: ' ' color=(192,197,206)
DEBUG:   Fragment 3: 'mut' color=(180,142,173)
DEBUG:   Fragment 4: ' sequence ' color=(192,197,206)
DEBUG:   Fragment 5: '=' color=(192,197,206)
DEBUG:   Fragment 6: ' ' color=(192,197,206)
DEBUG:   Fragment 7: 'vec!' color=(192,197,206)
DEBUG:   Fragment 8: '[' color=(192,197,206)
DEBUG:   Fragment 9: '0' color=(208,135,112)
DEBUG:   Fragment 10: ',' color=(192,197,206)
DEBUG:   Fragment 11: ' ' color=(192,197,206)
DEBUG:   Fragment 12: '1' color=(208,135,112)
DEBUG:   Fragment 13: ']' color=(192,197,206)
DEBUG:   Fragment 14: ';' color=(192,197,206)
DEBUG: Language: 'python', Syntax name: 'Python'
DEBUG: Processing 17 lines for language 'python'
DEBUG: Line 0: 'def calculate_fibonacci_sequence(n):' has 7 fragments
DEBUG:   Fragment 0: 'def' color=(180,142,173)
DEBUG:   Fragment 1: ' ' color=(192,197,206)
DEBUG:   Fragment 2: 'calculate_fibonacci_sequence' color=(143,161,179)
DEBUG:   Fragment 3: '(' color=(192,197,206)
DEBUG:   Fragment 4: 'n' color=(191,97,106)
DEBUG:   Fragment 5: ')' color=(192,197,206)
DEBUG:   Fragment 6: ':' color=(192,197,206)
DEBUG: Python fragment 'def' -> color=(180,142,173) -> ANSI='[31m'
DEBUG: Python fragment ' ' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment 'calculate_fibonacci_sequence' -> color=(143,161,179) -> ANSI='[34m'
DEBUG: Python fragment '(' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment 'n' -> color=(191,97,106) -> ANSI='[31m'
DEBUG: Python fragment ')' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment ':' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Line 1: '    sequence = [0, 1]' has 11 fragments
DEBUG:   Fragment 0: '    ' color=(192,197,206)
DEBUG:   Fragment 1: 'sequence' color=(192,197,206)
DEBUG:   Fragment 2: ' ' color=(192,197,206)
DEBUG:   Fragment 3: '=' color=(192,197,206)
DEBUG:   Fragment 4: ' ' color=(192,197,206)
DEBUG:   Fragment 5: '[' color=(192,197,206)
DEBUG:   Fragment 6: '0' color=(208,135,112)
DEBUG:   Fragment 7: ',' color=(192,197,206)
DEBUG:   Fragment 8: ' ' color=(192,197,206)
DEBUG:   Fragment 9: '1' color=(208,135,112)
DEBUG:   Fragment 10: ']' color=(192,197,206)
DEBUG: Python fragment '    ' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment 'sequence' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment ' ' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment '=' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment ' ' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment '[' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment '0' -> color=(208,135,112) -> ANSI='[31m'
DEBUG: Python fragment ',' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment ' ' -> color=(192,197,206) -> ANSI='[34m'
DEBUG: Python fragment '1' -> color=(208,135,112) -> ANSI='[31m'
DEBUG: Python fragment ']' -> color=(192,197,206) -> ANSI='[34m'
